import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/user.dart';
import '../theme/app_design_system.dart';
import 'starfield_background.dart';

/// 雷达扫描界面组件
class RadarScanWidget extends StatefulWidget {
  final List<User> aiUsers;
  final VoidCallback? onScanButtonPressed;
  final Function(User)? onUserSelected;
  final Function(User)? onScanComplete;
  final bool isScanning;
  final User? selectedUser;

  const RadarScanWidget({
    super.key,
    required this.aiUsers,
    this.onScanButtonPressed,
    this.onUserSelected,
    this.onScanComplete,
    this.isScanning = false,
    this.selectedUser,
  });

  @override
  State<RadarScanWidget> createState() => _RadarScanWidgetState();
}

class _RadarScanWidgetState extends State<RadarScanWidget>
    with TickerProviderStateMixin {
  late AnimationController _scanController;
  late AnimationController _fadeController;
  late AnimationController _floatController;
  late Animation<double> _scanAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _floatAnimation;

  @override
  void initState() {
    super.initState();
    
    // 扫描线旋转动画控制器
    _scanController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    // 头像淡入淡出动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 浮动动画控制器
    _floatController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _scanAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _scanController,
      curve: Curves.linear,
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _floatAnimation = Tween<double>(
      begin: -3.0,
      end: 3.0,
    ).animate(CurvedAnimation(
      parent: _floatController,
      curve: Curves.easeInOut,
    ));

    // 启动浮动动画循环
    _floatController.repeat(reverse: true);
    
    // 初始状态显示所有头像
    _fadeController.value = 0.0;
  }

  @override
  void didUpdateWidget(RadarScanWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isScanning && !oldWidget.isScanning) {
      _startScanAnimation();
    } else if (!widget.isScanning && oldWidget.isScanning) {
      _stopScanAnimation();
    }
  }

  void _startScanAnimation() {
    _fadeController.forward(); // 头像开始淡出
    _scanController.repeat(); // 扫描线开始旋转

    // 扫描完成后的逻辑
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted && widget.isScanning) {
        _completeScan();
      }
    });
  }

  void _stopScanAnimation() {
    _scanController.stop();
    _fadeController.reverse(); // 头像重新淡入
  }

  void _completeScan() {
    if (widget.aiUsers.isNotEmpty) {
      // 随机选择一个 AI 角色
      final random = math.Random();
      final selectedUser = widget.aiUsers[random.nextInt(widget.aiUsers.length)];

      // 停止扫描动画
      _stopScanAnimation();

      // 延迟一下再触发回调，让动画完成
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          widget.onScanComplete?.call(selectedUser);
        }
      });
    }
  }

  @override
  void dispose() {
    _scanController.dispose();
    _fadeController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenSize = math.min(size.width, size.height);
    // 增大雷达半径，让它更好地填充屏幕
    final radarRadius = screenSize * 0.45;

    // 调整雷达半径范围，让它更大
    final clampedRadius = radarRadius.clamp(150.0, 300.0);

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // 2.5D星空背景
          Positioned.fill(
            child: StarfieldBackground(
              width: size.width,
              height: size.height,
            ),
          ),

          // 雷达网格叠加层（保持雷达感觉）
          Positioned.fill(
            child: RepaintBoundary(
              child: CustomPaint(
                painter: RadarGridPainter(
                  centerX: size.width / 2,
                  centerY: size.height / 2,
                  radius: clampedRadius,
                ),
              ),
            ),
          ),

          // 扫描线
          if (widget.isScanning)
            Positioned.fill(
              child: RepaintBoundary(
                child: AnimatedBuilder(
                  animation: _scanAnimation,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: ScanLinePainter(
                        angle: _scanAnimation.value,
                        centerX: size.width / 2,
                        centerY: size.height / 2,
                        radius: clampedRadius,
                      ),
                    );
                  },
                ),
              ),
            ),

          // AI 角色头像 - 分布在多个圆周上
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return _buildAvatarCircles(size, clampedRadius);
              },
            ),
          ),

          // 中心扫描按钮 - 精确定位到雷达中心
          _buildCenteredScanButton(size, screenSize),
        ],
      ),
    );
  }

  Widget _buildAvatarCircles(Size screenSize, double radarRadius) {
    final avatarCount = widget.aiUsers.length;
    if (avatarCount == 0) return const SizedBox.shrink();

    // 根据屏幕大小调整头像基础大小
    final baseAvatarSize = (math.min(screenSize.width, screenSize.height) * 0.08).clamp(40.0, 70.0);

    final centerX = screenSize.width / 2;
    final centerY = screenSize.height / 2;

    List<Widget> avatarWidgets = [];

    // 生成不规则但美观的AI角色分布
    List<Offset> usedPositions = [];

    for (int i = 0; i < avatarCount; i++) {
      final user = widget.aiUsers[i];

      // 使用用户ID作为随机种子，确保位置固定但看起来随机
      final random = math.Random(user.id.hashCode);

      // 为每个头像计算不同的大小（0.8-1.2倍变化）
      final sizeVariation = 0.8 + random.nextDouble() * 0.4; // 0.8-1.2倍
      final avatarSize = baseAvatarSize * sizeVariation;
      final halfAvatarSize = avatarSize / 2;

      Offset? finalPosition;
      int attempts = 0;
      const maxAttempts = 50;

      while (finalPosition == null && attempts < maxAttempts) {
        // 根据索引和随机性选择圆周层级
        final layerIndex = (i + random.nextInt(3)) % 4; // 0-3层
        final layerProgress = layerIndex / 3.0; // 0.0 到 1.0

        // 计算半径：内层较密集，外层较稀疏
        final minRadius = radarRadius * 0.35;
        final maxRadius = radarRadius * 0.85;
        final baseRadius = minRadius + (maxRadius - minRadius) * layerProgress;

        // 添加随机半径变化
        final radiusVariation = radarRadius * 0.1;
        final actualRadius = baseRadius + (random.nextDouble() - 0.5) * radiusVariation;

        // 生成角度：结合随机性和分散性
        final baseAngle = (i * 2.4) % (2 * math.pi); // 基础分散角度
        final randomAngle = random.nextDouble() * math.pi * 0.8; // 随机偏移
        final finalAngle = baseAngle + randomAngle;

        // 计算候选位置
        final candidateX = centerX + actualRadius * math.cos(finalAngle);
        final candidateY = centerY + actualRadius * math.sin(finalAngle);

        // 边界检查（考虑当前头像的实际大小）
        if (candidateX < halfAvatarSize + 20 ||
            candidateX > screenSize.width - halfAvatarSize - 20 ||
            candidateY < halfAvatarSize + 120 ||
            candidateY > screenSize.height - halfAvatarSize - 120) {
          attempts++;
          continue;
        }

        final candidatePosition = Offset(candidateX, candidateY);

        // 检查与已有位置的距离，避免重叠
        bool tooClose = false;
        final minDistance = avatarSize * 1.2; // 最小距离

        for (final usedPos in usedPositions) {
          if ((candidatePosition - usedPos).distance < minDistance) {
            tooClose = true;
            break;
          }
        }

        if (!tooClose) {
          finalPosition = candidatePosition;
          usedPositions.add(finalPosition);
        }

        attempts++;
      }

      // 如果找不到合适位置，使用备用算法
      if (finalPosition == null) {
        final fallbackAngle = (i * 2 * math.pi) / avatarCount;
        final fallbackRadius = radarRadius * (0.5 + (i % 3) * 0.15);
        finalPosition = Offset(
          centerX + fallbackRadius * math.cos(fallbackAngle),
          centerY + fallbackRadius * math.sin(fallbackAngle),
        );
      }

      avatarWidgets.add(
        Positioned(
          left: finalPosition.dx - halfAvatarSize,
          top: finalPosition.dy - halfAvatarSize,
          child: Opacity(
            opacity: widget.isScanning ? _fadeAnimation.value : 1.0,
            child: _buildUserAvatar(user, avatarSize, i),
          ),
        ),
      );
    }

    return Stack(children: avatarWidgets);
  }

  Widget _buildUserAvatar(User user, double avatarSize, int index) {
    final fontSize = (avatarSize * 0.2).clamp(10.0, 14.0);

    // 使用用户ID生成一致的随机效果
    final random = math.Random(user.id.hashCode);

    // 随机的轻微旋转角度，让头像看起来更自然
    final rotationAngle = (random.nextDouble() - 0.5) * 0.2; // ±0.1弧度

    // 随机的轻微缩放，增加视觉层次
    final scaleVariation = 0.9 + random.nextDouble() * 0.2; // 0.9-1.1倍

    return GestureDetector(
      onTap: () => widget.onUserSelected?.call(user),
      child: AnimatedBuilder(
        animation: _floatAnimation,
        builder: (context, child) {
          // 为每个头像创建不同的浮动效果
          final phaseOffset = (index * 0.5) % (2 * math.pi);
          final customFloat = math.sin(_floatController.value * 2 * math.pi + phaseOffset) * 3.0;

          return Transform.translate(
            offset: Offset(0, customFloat),
            child: Transform.rotate(
              angle: rotationAngle,
              child: Transform.scale(
                scale: scaleVariation,
                child: child!,
              ),
            ),
          );
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: avatarSize,
              height: avatarSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.8),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                  // 添加内发光效果
                  BoxShadow(
                    color: AppDesignSystem.primaryYellow.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  user.avatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.person,
                        color: Colors.grey,
                        size: avatarSize * 0.5,
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: avatarSize * 0.1),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: avatarSize * 0.1,
                vertical: avatarSize * 0.05,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(avatarSize * 0.15),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 0.5,
                ),
              ),
              child: Text(
                user.name,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: fontSize,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenteredScanButton(Size screenSize, double screenDimension) {
    final buttonSize = (screenDimension * 0.12).clamp(60.0, 100.0);
    final iconSize = buttonSize * 0.4;

    // 计算雷达中心点
    final centerX = screenSize.width / 2;
    final centerY = screenSize.height / 2;

    return Positioned(
      left: centerX - buttonSize / 2,
      top: centerY - buttonSize / 2,
      child: GestureDetector(
        onTap: widget.onScanButtonPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppDesignSystem.primaryYellow.withValues(alpha: 0.9),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.9),
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: (AppDesignSystem.primaryYellow)
                    .withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
              // 添加内发光效果，让按钮更突出
              BoxShadow(
                color: (AppDesignSystem.primaryYellow)
                    .withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 0),
              ),
              // 添加脉冲效果（扫描时）
              if (widget.isScanning)
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 0),
                ),
            ],
          ),
          child: Icon(
            widget.isScanning ? Icons.stop : Icons.radar,
            color: Colors.white,
            size: iconSize,
          ),
        ),
      ),
    );
  }
}

/// 雷达网格绘制器（仅绘制网格线，不绘制背景）
class RadarGridPainter extends CustomPainter {
  final double? centerX;
  final double? centerY;
  final double? radius;

  RadarGridPainter({this.centerX, this.centerY, this.radius});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(
      centerX ?? size.width / 2,
      centerY ?? size.height / 2,
    );
    final radarRadius = radius ?? size.width / 2;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    // 绘制同心圆
    for (int i = 1; i <= 4; i++) {
      paint.color = AppDesignSystem.primaryYellow.withValues(alpha: 0.08 + (i * 0.04));
      canvas.drawCircle(center, radarRadius * i / 4, paint);
    }

    // 绘制十字线
    paint.color = AppDesignSystem.primaryYellow.withValues(alpha: 0.15);
    canvas.drawLine(
      Offset(center.dx - radarRadius, center.dy),
      Offset(center.dx + radarRadius, center.dy),
      paint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - radarRadius),
      Offset(center.dx, center.dy + radarRadius),
      paint,
    );

    // 绘制外圆边框
    paint.color = AppDesignSystem.primaryYellow.withValues(alpha: 0.3);
    paint.strokeWidth = 3;
    canvas.drawCircle(center, radarRadius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 扫描线绘制器
class ScanLinePainter extends CustomPainter {
  final double angle;
  final double? centerX;
  final double? centerY;
  final double radius;

  ScanLinePainter({
    required this.angle,
    required this.radius,
    this.centerX,
    this.centerY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(
      centerX ?? size.width / 2,
      centerY ?? size.height / 2,
    );

    // 扫描线渐变
    final gradient = RadialGradient(
      colors: [
        AppDesignSystem.primaryYellow.withValues(alpha: 0.8),
        AppDesignSystem.primaryYellow.withValues(alpha: 0.4),
        Colors.transparent,
      ],
      stops: const [0.0, 0.3, 1.0],
    );
    
    final paint = Paint()
      ..shader = gradient.createShader(Rect.fromCircle(center: center, radius: radius));
    
    // 绘制扫描扇形
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      angle - math.pi / 6,
      math.pi / 3,
      true,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
