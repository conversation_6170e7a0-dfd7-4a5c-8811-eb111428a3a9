import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/ai_data_service.dart';
import '../components/radar_scan_widget.dart';
import '../components/ai_role_detail_card.dart';
import '../theme/app_design_system.dart';
import 'chat_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AIRolePage extends StatefulWidget {
  const AIRolePage({super.key});

  @override
  State<AIRolePage> createState() => _AIRolePageState();
}

class _AIRolePageState extends State<AIRolePage> with AutomaticKeepAliveClientMixin {
  List<User> _aiUsers = [];
  List<User> _displayedUsers = [];
  bool _hasLoadedData = false;
  bool _isScanning = false;
  User? _selectedUser;
  static const int _maxDisplayCount = 10;
  static const int _minDisplayCount = 8;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadAIUsersIfNeeded();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadAIUsersIfNeeded() async {
    if (_hasLoadedData) return;
    await _loadAIUsers();
  }

  Future<void> _loadAIUsers() async {
    final users = await AIDataService().getAllUsers();
    if (mounted) {
      setState(() {
        _aiUsers = users;
        _hasLoadedData = true;
        _selectRandomUsersToDisplay();
      });
    }
  }

  void _selectRandomUsersToDisplay() {
    if (_aiUsers.isEmpty) return;

    // 随机选择8-10个用户显示
    final random = DateTime.now().millisecondsSinceEpoch;
    final displayCount = _minDisplayCount + (random % (_maxDisplayCount - _minDisplayCount + 1));

    final shuffledUsers = List<User>.from(_aiUsers);
    shuffledUsers.shuffle();

    _displayedUsers = shuffledUsers.take(displayCount).toList();
  }

  void _startScan() {
    if (_aiUsers.isEmpty) return;

    setState(() {
      _isScanning = true;
      _selectedUser = null;
    });
  }

  void _stopScan() {
    setState(() {
      _isScanning = false;
    });
  }

  void _toggleScan() {
    if (_isScanning) {
      _stopScan();
    } else {
      _startScan();
    }
  }

  void _onScanComplete(User selectedUser) {
    setState(() {
      _isScanning = false;
      _selectedUser = selectedUser;
      // 扫描完成后重新随机选择显示的用户
      _selectRandomUsersToDisplay();
    });
  }

  void _onUserSelected(User user) {
    setState(() {
      _selectedUser = user;
    });
  }

  void _onStartChat(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(aiUser: user),
      ),
    );
  }

  void _onRescan() {
    setState(() {
      _selectedUser = null;
    });
    _startScan();
  }

  void _onCloseDetailCard() {
    setState(() {
      _selectedUser = null;
    });
  }



  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: AppDesignSystem.primaryBlack,
      body: !_hasLoadedData || _displayedUsers.isEmpty
          ? const Center(
              child: CircularProgressIndicator(
                color: AppDesignSystem.primaryYellow,
              ),
            )
          : Stack(
              children: [
                // 雷达扫描界面
                RadarScanWidget(
                  aiUsers: _displayedUsers,
                  isScanning: _isScanning,
                  onScanButtonPressed: _toggleScan,
                  onUserSelected: _onUserSelected,
                  onScanComplete: _onScanComplete,
                ),

                // Fixed header
                Positioned(
                  top: 50,
                  left: 20,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.aiRole,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AppDesignSystem.primaryYellow,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),

                // AI 角色详细资料卡片
                if (_selectedUser != null)
                  AIRoleDetailCard(
                    aiUser: _selectedUser!,
                    onStartChat: () => _onStartChat(_selectedUser!),
                    onRescan: _onRescan,
                    onClose: _onCloseDetailCard,
                  ),
              ],
            ),
    );
  }

}